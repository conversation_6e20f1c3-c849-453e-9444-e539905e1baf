/* Internal Pages Styles */

/* Global Styles for Pages */
*{
margin: 0;
padding: 0;
box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* Navigation Styles - Same as Home Page */
nav-div{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2%;
    background: #BFECFF;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 0.5px solid rgba(0, 0, 170, 0.329);
    height: 80px;
}

nav img{
    height: 80px;
    filter: drop-shadow(2px 2px 6px rgba(255, 255, 255, 0.8));
}

ul {
    display: flex;
    align-items: center;
    justify-content: space-between;
    list-style: none;
    gap: 3rem;
}

ul li a {
    text-decoration: none;
    color: navy;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

ul li a:hover {
    color: #3b82f6;
}

ul button{
    padding: 0.7rem 1.5rem;
    border-radius: 20px;
    background-color: navy;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
}

ul button a{
    text-decoration: none;
    color: white;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: absolute;
    background: white;
    min-width: 320px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border-radius: 20px;
    padding: 20px 0;
    top: 100%;
    left: 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
    opacity: 0;
    transform: translateY(-15px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    overflow: hidden;
    margin-top: 10px;
}

.dropdown-content::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 30px;
    width: 16px;
    height: 16px;
    background: white;
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    z-index: -1;
}

.dropdown:hover .dropdown-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: dropdownFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-15px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown-content a {
    color: #1e3a8a !important;
    padding: 18px 25px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 18px;
    font-size: 1rem !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(241, 245, 249, 0.8);
    position: relative;
    overflow: hidden;
}

.dropdown-content a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scaleY(0);
    transition: transform 0.3s ease;
    transform-origin: bottom;
}

.dropdown-content a:last-child {
    border-bottom: none;
}

.dropdown-content a:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    color: #1e40af !important;
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

.dropdown-content a:hover::before {
    transform: scaleY(1);
    transform-origin: top;
}

.dropdown-content a i {
    font-size: 1.6rem !important;
    color: #3b82f6;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
}

.dropdown-content a:hover i {
    color: #1e40af;
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.2));
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.dropdown-content a div {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.dropdown-content a strong {
    font-weight: 700;
    font-size: 1.1rem;
    color: #1e3a8a;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.dropdown-content a span {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.4;
    transition: all 0.3s ease;
}

.dropdown-content a:hover strong {
    color: #1e40af;
    transform: translateX(2px);
}

.dropdown-content a:hover span {
    color: #4b5563;
    transform: translateX(2px);
}

.dropdown > a i {
    font-size: 0.9rem;
    margin-left: 8px;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown:hover > a i {
    transform: rotate(180deg);
}

/* Mobile Navigation */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: navy;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background-color: #BFECFF;
    z-index: 10001;
    transition: top 0.4s ease-in-out;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 139, 0.2);
}

.mobile-menu.active {
    top: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: #BFECFF;
    border-bottom: 1px solid rgba(0, 0, 139, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.mobile-logo {
    height: 50px;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
    transition: transform 0.3s ease;
}

.mobile-logo:hover {
    transform: scale(1.05);
}

.mobile-menu-close {
    font-size: 1.8rem;
    color: navy;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.1) 0%, rgba(255, 71, 87, 0.05) 100%);
    border: none;
}

.mobile-menu-content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #BFECFF;
}

.mobile-menu-links {
    list-style: none;
    padding: 0;
    margin: 0;
    background: #BFECFF;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 10px;
    padding: 20px 0;
}

.mobile-menu-links li {
    border-bottom: 1px solid rgba(0, 0, 139, 0.1);
}

.mobile-menu-links a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 1.2rem 2rem;
    color: navy;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    background: #BFECFF;
}

.mobile-menu-links a.active {
    background: rgba(0, 0, 139, 0.15);
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

.mobile-menu-links a:hover {
    background: rgba(0, 0, 139, 0.1);
    color: #3b82f6;
    padding-left: 2.5rem;
}

.mobile-menu-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.mobile-menu-links a:hover::before {
    transform: scaleY(1);
}

.mobile-menu-links a i {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.mobile-apply-btn {
    padding: 20px;
    background: #BFECFF;
    border-top: 1px solid rgba(0, 0, 139, 0.1);
}

.mobile-apply-btn a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, navy, #1e40af);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 139, 0.3);
}

.mobile-apply-btn a:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 139, 0.4);
}

/* Desktop Menu - Default Visible */
.desktop-menu {
    display: block;
}

/* Mobile Responsive Navigation */
@media (max-width: 768px) {
    nav-div {
        padding: 0 1rem;
        height: 70px;
    }

    .desktop-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu {
        display: block;
    }

    nav img {
        height: 60px;
    }

    .dropdown-content {
        min-width: 280px;
        padding: 15px 0;
        border-radius: 15px;
        margin-top: 8px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    }

    .dropdown-content::before {
        left: 20px;
        width: 12px;
        height: 12px;
        top: -6px;
    }

    .dropdown-content a {
        padding: 15px 20px;
        gap: 15px;
    }

    .dropdown-content a i {
        width: 40px;
        height: 40px;
        font-size: 1.4rem !important;
        border-radius: 10px;
    }

    .dropdown-content a strong {
        font-size: 1rem;
    }

    .dropdown-content a span {
        font-size: 0.85rem;
    }

    .dropdown-content a:hover {
        transform: translateX(5px);
    }
}

/* Apply Visa CTA Section */
.apply-visa-cta {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.apply-visa-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center center / cover no-repeat;
    opacity: 0.1;
    z-index: 1;
}

.apply-visa-cta .cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.apply-visa-cta h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 15px;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.apply-visa-cta h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 25px;
    line-height: 1.2;
}

.apply-visa-cta p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
}

.apply-btn {
    display: inline-block;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 18px 40px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.apply-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, #059669, #047857);
}

/* Footer Styles - Same as Home Page */
.main-footer {
    background: url('images/hero/Footer.jpg');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    padding: 80px 0 40px;
    position: relative;
    color: white;
}

.main-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.footer-content {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr;
    gap: 60px;
    margin-bottom: 50px;
    align-items: start;
}

.footer-left {
    padding-right: 40px;
}

.footer-logo {
    width: 140px;
    height: auto;
    margin-bottom: 30px;
    filter: drop-shadow(3px 3px 8px rgba(0, 0, 0, 0.7));
}

.footer-tagline {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.1rem;
    margin-bottom: 15px;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
}

.footer-slogan {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 35px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    line-height: 1.4;
    letter-spacing: 0.5px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.social-links a {
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-links a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.footer-middle h4,
.footer-right h4 {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 0.5px;
    position: relative;
}

.footer-middle ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-middle ul li {
    display: block;
    width: 100%;
}

.footer-middle ul li a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    display: block;
    padding: 5px 0;
}

.footer-middle ul li a:hover {
    color: white;
    padding-left: 8px;
    font-weight: 500;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-details p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 400;
    line-height: 1.5;
    margin: 0;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 30px;
    text-align: center;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Footer Mobile Responsive */
@media (max-width: 768px) {
    .main-footer {
        padding: 60px 0 30px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-left {
        padding-right: 0;
    }

    .footer-logo {
        width: 120px;
        margin: 0 auto 25px;
    }

    .footer-tagline {
        font-size: 1rem;
        margin-bottom: 12px;
    }

    .footer-slogan {
        font-size: 1.2rem;
        margin-bottom: 25px;
    }

    .social-links {
        justify-content: center;
        gap: 12px;
    }

    .footer-middle h4,
    .footer-right h4 {
        font-size: 1.2rem;
        margin-bottom: 20px;
    }

    .footer-middle ul {
        gap: 10px;
    }

    .footer-middle ul li a {
        font-size: 1rem;
        padding: 8px 0;
    }

    .contact-details {
        gap: 12px;
    }

    .contact-details p {
        font-size: 1rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}

/* About Page Specific Styles */
.about-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.3);
    z-index: 2;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    animation: float 6s ease-in-out infinite;
}

.hero-shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.hero-shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: 5%;
    animation-delay: 2s;
}

.hero-shape-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 3;
    width: 100%;
}

.about-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text-content {
    animation: slideInLeft 1s ease-out;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-badge i {
    font-size: 1rem;
}

.hero-text-content h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
    font-weight: 400;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    padding: 30px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #60a5fa;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    gap: 20px;
    align-items: center;
}

.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.hero-visual {
    animation: slideInRight 1s ease-out;
}

.hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
}

.hero-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    display: block;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 40px 30px 30px;
}

.overlay-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #1e3a8a;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Company Introduction Section */
.company-intro-section {
    background: white;
    padding: 100px 0;
    position: relative;
}

.intro-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.intro-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
}

.intro-text {
    padding-right: 20px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e3a8a;
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    border: 1px solid #e0e7ff;
}

.section-badge i {
    font-size: 1rem;
}

.intro-text h2 {
    font-size: 2.8rem;
    font-weight: 800;
    color: #1e3a8a;
    line-height: 1.2;
    margin-bottom: 30px;
}

.intro-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #6b7280;
    margin-bottom: 25px;
}

.intro-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 40px;
}

.intro-feature {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.intro-feature:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.1);
}

.intro-feature i {
    font-size: 1.2rem;
    color: #10b981;
    background: #d1fae5;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.intro-feature span {
    font-weight: 600;
    color: #374151;
    font-size: 1rem;
}

.intro-visual {
    position: relative;
}

.visual-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.visual-item {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.visual-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.visual-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.visual-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.4s ease;
}

.visual-item:hover .visual-icon {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scale(1.1);
}

.visual-icon i {
    font-size: 1.5rem;
    color: #3b82f6;
    transition: all 0.4s ease;
}

.visual-item:hover .visual-icon i {
    color: white;
}

.visual-item h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 12px;
}

.visual-item p {
    color: #6b7280;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Company Story Section */
.company-story-section {
    background: white;
    padding: 100px 0;
}

.story-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.story-highlights {
    margin-top: 40px;
}

.highlight-item {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: flex-start;
}

.highlight-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.highlight-icon i {
    font-size: 1.2rem;
    color: #3b82f6;
}

.highlight-content h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.highlight-content p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

.story-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.story-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.story-overlay {
    position: absolute;
    bottom: 30px;
    left: 30px;
    right: 30px;
}

.milestone-badge {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.milestone-number {
    font-size: 2rem;
    font-weight: 800;
    color: #1e3a8a;
    display: block;
    line-height: 1;
}

.milestone-text {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 600;
    margin-top: 5px;
}

/* Services Overview Section */
.services-overview-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.services-overview-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.service-overview-card {
    background: white;
    border-radius: 20px;
    padding: 35px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.service-overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.service-overview-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.service-overview-card .service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}

.service-overview-card .service-icon i {
    font-size: 1.8rem;
    color: #3b82f6;
}

.service-overview-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.service-overview-card p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-overview-card ul {
    list-style: none;
    margin-bottom: 25px;
}

.service-overview-card ul li {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.service-overview-card ul li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    width: 16px;
    height: 16px;
    background: #d1fae5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.service-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.service-link:hover {
    color: #1e40af;
    transform: translateX(5px);
}

.service-link i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.service-link:hover i {
    transform: translateX(3px);
}

/* Why Choose Us Section */
.why-choose-section {
    background: white;
    padding: 100px 0;
}

.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.why-choose-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.why-choose-features {
    margin-top: 40px;
}

.why-feature-item {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: flex-start;
}

.why-feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.why-feature-icon i {
    font-size: 1.2rem;
    color: white;
}

.why-feature-content h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.why-feature-content p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

.achievement-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.achievement-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.achievement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.achievement-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.achievement-icon i {
    font-size: 1.5rem;
    color: white;
}

.achievement-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: #1e3a8a;
    line-height: 1;
    margin-bottom: 8px;
}

.achievement-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 600;
}

/* Mobile Responsive for About Page */
@media (max-width: 768px) {
    .about-hero-section {
        padding: 100px 0 60px 0;
        min-height: auto;
    }

    .about-hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text-content h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
        margin-bottom: 30px;
        padding: 20px 0;
    }

    .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        text-align: left;
    }

    .stat-number {
        font-size: 2rem;
        margin-bottom: 0;
    }

    .hero-actions {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .btn-primary, .btn-secondary {
        justify-content: center;
        padding: 12px 25px;
    }

    .hero-image {
        height: 350px;
    }

    .company-intro-section {
        padding: 60px 0;
    }

    .intro-content {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .intro-text {
        padding-right: 0;
        text-align: center;
    }

    .intro-text h2 {
        font-size: 2.2rem;
        margin-bottom: 25px;
    }

    .intro-description {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .intro-features {
        margin-top: 30px;
    }

    .intro-feature {
        padding: 12px 15px;
        justify-content: center;
    }

    .visual-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .visual-item {
        padding: 25px 20px;
    }

    .visual-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
    }

    .visual-icon i {
        font-size: 1.3rem;
    }

    .visual-item h4 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .visual-item p {
        font-size: 0.9rem;
    }

    .company-story-section,
    .services-overview-section,
    .why-choose-section {
        padding: 60px 0;
    }

    .story-content,
    .why-choose-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
    }

    .service-overview-card {
        padding: 25px;
    }

    .achievement-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .story-image img {
        height: 300px;
    }

    .milestone-badge {
        padding: 15px;
    }

    .milestone-number {
        font-size: 1.5rem;
    }

    .highlight-item,
    .why-feature-item {
        margin-bottom: 25px;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .hero-shape-1 {
        width: 200px;
        height: 200px;
        top: 5%;
        right: 5%;
    }

    .hero-shape-2 {
        width: 150px;
        height: 150px;
        bottom: 10%;
        left: 5%;
    }

    .hero-shape-3 {
        width: 100px;
        height: 100px;
        top: 40%;
        left: 40%;
    }
}

@media (max-width: 480px) {
    .about-hero-section {
        padding: 80px 0 50px 0;
    }

    .hero-text-content h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .intro-text h2 {
        font-size: 1.8rem;
    }

    .section-badge {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    .intro-feature {
        padding: 10px 12px;
    }

    .intro-feature span {
        font-size: 0.9rem;
    }

    .visual-item {
        padding: 20px 15px;
    }

    .hero-actions {
        gap: 12px;
    }

    .btn-primary, .btn-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    /* Enhanced Achievements Mobile Styles */
    .enhanced-achievements-section {
        padding: 80px 0;
    }

    .achievements-header h2 {
        font-size: 2.2rem;
        margin-bottom: 20px;
    }

    .achievements-header p {
        font-size: 1rem;
        margin-bottom: 50px;
    }

    .achievements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 40px;
    }

    .achievement-card {
        padding: 25px 20px;
        min-height: 240px;
    }

    .achievement-icon {
        width: 60px;
        height: 60px;
    }

    .achievement-icon i {
        font-size: 1.5rem;
    }

    .icon-pulse {
        width: 60px;
        height: 60px;
    }

    .count {
        font-size: 2.5rem;
    }

    .number-suffix {
        font-size: 1.5rem;
    }

    .achievement-content h3 {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .achievement-content p {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .footer-stats {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }

    .footer-stat {
        font-size: 0.9rem;
    }

    .featured-ribbon {
        font-size: 0.7rem;
        padding: 6px 30px;
        right: -25px;
    }

    .floating-element {
        display: none;
    }
}

@media (max-width: 480px) {
    .enhanced-achievements-section {
        padding: 60px 0;
    }

    .achievements-header h2 {
        font-size: 1.8rem;
    }

    .header-badge {
        font-size: 0.8rem;
        padding: 8px 16px;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .achievement-card {
        padding: 20px 15px;
        min-height: 200px;
    }

    .count {
        font-size: 2.2rem;
    }

    .number-suffix {
        font-size: 1.4rem;
    }

    .achievement-content h3 {
        font-size: 1rem;
    }

    .achievement-content p {
        font-size: 0.8rem;
    }

    .achievement-icon {
        width: 50px;
        height: 50px;
    }

    .achievement-icon i {
        font-size: 1.3rem;
    }

    .icon-pulse {
        width: 50px;
        height: 50px;
    }
}

/* About Main Content Section */
.about-main-section {
    background: white;
    padding: 80px 0;
}

.about-main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 60px;
}

.about-text-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.intro-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #6b7280;
    margin-bottom: 25px;
}

.intro-text:last-child {
    margin-bottom: 0;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
}

.feature-card .feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scale(1.1);
}

.feature-card .feature-icon i {
    font-size: 1.8rem;
    color: #3b82f6;
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon i {
    color: white;
}

.feature-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.feature-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Mission Section */
.mission-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 80px 0;
}

.mission-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.mission-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 30px;
}

.mission-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #6b7280;
    margin-bottom: 40px;
}

.mission-cta {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 18px 40px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.4s ease;
    display: inline-block;
}

.mission-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.3);
}

/* Enhanced Achievements Section */
.enhanced-achievements-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0;
    position: relative;
    overflow: hidden;
    color: white;
}

.achievements-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.achievements-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 23, 42, 0.2);
    z-index: 2;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.floating-element {
    position: absolute;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 50%;
    animation: floatUpDown 8s ease-in-out infinite;
}

.element-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 150px;
    height: 150px;
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 100px;
    height: 100px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.element-4 {
    width: 120px;
    height: 120px;
    bottom: 15%;
    right: 10%;
    animation-delay: 6s;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-30px) rotate(180deg); opacity: 0.6; }
}

.achievements-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 3;
}

.achievements-header {
    text-align: center;
    margin-bottom: 80px;
    animation: fadeInUp 1s ease-out;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    padding: 12px 24px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-badge i {
    font-size: 1.1rem;
    color: #fbbf24;
}

.achievements-header h2 {
    font-size: 3.2rem;
    font-weight: 800;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.achievements-header p {
    font-size: 1.2rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin: 0 auto;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 60px;
}

.achievement-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInUp 1s ease-out;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.achievement-card:nth-child(1) { animation-delay: 0.1s; }
.achievement-card:nth-child(2) { animation-delay: 0.2s; }
.achievement-card:nth-child(3) { animation-delay: 0.3s; }
.achievement-card:nth-child(4) { animation-delay: 0.4s; }

.card-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.achievement-card:hover .card-glow {
    opacity: 1;
}

.achievement-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.featured-card {
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(251, 191, 36, 0.3);
    position: relative;
}

.featured-ribbon {
    position: absolute;
    top: 20px;
    right: -30px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #1e3a8a;
    padding: 8px 40px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transform: rotate(45deg);
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.achievement-icon-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.achievement-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.2));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
}

.achievement-card:hover .achievement-icon {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.achievement-icon i {
    font-size: 1.5rem;
    color: #60a5fa;
    transition: all 0.4s ease;
}

.achievement-card:hover .achievement-icon i {
    color: white;
}

.icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    border: 2px solid rgba(96, 165, 250, 0.3);
    border-radius: 15px;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(1.3); opacity: 0; }
}

.achievement-content {
    position: relative;
    z-index: 2;
}

.achievement-number {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 3px;
    margin-bottom: 12px;
}

.count {
    font-size: 2.8rem;
    font-weight: 800;
    color: #ffffff;
    line-height: 1;
}

.number-suffix {
    font-size: 1.6rem;
    font-weight: 700;
    color: #60a5fa;
}

.achievement-content h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 8px;
}

.achievement-content p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    font-size: 0.85rem;
    margin-bottom: 15px;
}

.achievement-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    border-radius: 2px;
    width: 0%;
    transition: width 2s ease-in-out;
    animation: progressFill 2s ease-in-out forwards;
}

@keyframes progressFill {
    from { width: 0%; }
    to { width: var(--progress, 100%); }
}

.achievements-footer {
    text-align: center;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    flex-wrap: wrap;
}

.footer-stat {
    display: flex;
    align-items: center;
    gap: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 1rem;
}

.footer-stat i {
    font-size: 1.2rem;
    color: #10b981;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-section .stat-item {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.stats-section .stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.stats-section .stat-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
}

.stats-section .stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
}

.stats-section .stat-icon i {
    font-size: 1.8rem;
    color: white;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #1e3a8a;
    line-height: 1;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.stat-description {
    font-size: 0.9rem;
    color: #6b7280;
}

/* Mobile Responsive for About Main Content */
@media (max-width: 768px) {
    .about-main-section,
    .mission-section,
    .stats-section {
        padding: 60px 0;
    }

    .features-grid,
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 40px;
    }

    .feature-card,
    .stats-section .stat-item {
        padding: 25px;
    }

    .mission-content h2,
    .stats-header h2 {
        font-size: 2rem;
    }

    .mission-text {
        font-size: 1.1rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .intro-text {
        font-size: 1rem;
    }
}

/* Contact Page Specific Styles */
.contact-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.contact-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.contact-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Quick Contact Bar */
.quick-contact-bar {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 40px 0;
    border-bottom: 1px solid #e5e7eb;
}

.quick-contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.quick-contact-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.quick-contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    background: white;
    padding: 25px;
    border-radius: 15px;
    text-decoration: none;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.quick-contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.quick-contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.quick-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.quick-icon i {
    font-size: 1.3rem;
    color: #3b82f6;
}

.quick-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.quick-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 600;
}

.quick-value {
    font-size: 1.1rem;
    color: #1e3a8a;
    font-weight: 700;
}

/* Contact Form Section */
.contact-form-section {
    background: white;
    padding: 80px 0;
}

.contact-form-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.contact-form-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-form {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 40px;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    color: #374151;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 15px 40px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.submit-btn i {
    font-size: 1rem;
}

/* Contact Info Section */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-info-item {
    background: white;
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.contact-info-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.contact-info-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.contact-info-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-info-icon i {
    font-size: 1.2rem;
    color: white;
}

.contact-info-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e3a8a;
}

.contact-info-details {
    color: #6b7280;
    line-height: 1.6;
}

.contact-info-details a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
}

.contact-info-details a:hover {
    color: #1e40af;
}

/* Mobile Responsive for Contact Page */
@media (max-width: 768px) {
    .contact-hero-section {
        padding: 80px 0 60px 0;
    }

    .contact-hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .quick-contact-bar {
        padding: 30px 0;
    }

    .quick-contact-items {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .quick-contact-item {
        padding: 20px;
    }

    .contact-form-section {
        padding: 60px 0;
    }

    .contact-form-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-form {
        padding: 30px 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .contact-info {
        gap: 20px;
    }

    .contact-info-item {
        padding: 25px;
    }
}

/* FEATURES / DECORATIVE SECTION */
.features-area {
  background: #f4f7fa;
  padding: 60px 0;
}
.features-area h2 {
  text-align: center;
  color: #000080;
  font-size: 2rem;
  margin-bottom: 40px;
}
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.features-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.icon-wrapper {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.icon-wrapper img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 50%;
}
.feature {
  background: #fff;
  border-radius: 8px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}
.feature img {
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}
.feature h3 {
  color: #000080;
  font-size: 1.2rem;
  margin-bottom: 10px;
}
.feature p {
  font-size: 0.95rem;
  color: #333;
  line-height: 1.4;
}
.feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

/* Responsive tweak */
@media (max-width: 480px) {
  .features-area {
    padding: 40px 0;
  }
  .feature {
    padding: 20px 15px;
  }
}

/* Detailed Visa Contact Form */
.detailed-contact-area {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
    position: relative;
}

.detailed-contact-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center center / cover no-repeat;
    opacity: 0.05;
    z-index: 1;
}

.detailed-contact-area .wrapper {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.8rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 20px;
    line-height: 1.2;
}

.section-header p {
    font-size: 1.2rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.detailed-visa-form {
    background: white;
    border-radius: 16px;
    padding: 50px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.detailed-visa-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #3b82f6, #00d4aa, #f59e0b);
    border-radius: 16px 16px 0 0;
}

.form-section {
    margin-bottom: 45px;
    padding-bottom: 35px;
    border-bottom: 1px solid #e5e7eb;
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 35px;
}

.form-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #00d4aa, #3b82f6);
    border-radius: 2px;
}

.grid {
    display: grid;
    gap: 25px;
    margin-bottom: 25px;
}

.two-cols {
    grid-template-columns: 1fr 1fr;
}

.field {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
}

.field.full {
    grid-column: 1 / -1;
}

.field label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 5px;
}

.field input,
.field select,
.field textarea {
    padding: 15px 18px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    color: #1f2937;
    background: white;
    transition: all 0.3s ease;
    font-family: inherit;
}

.field input:focus,
.field select:focus,
.field textarea:focus {
    outline: none;
    border-color: #00d4aa;
    box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.field input::placeholder,
.field textarea::placeholder {
    color: #9ca3af;
}

.field textarea {
    resize: vertical;
    line-height: 1.6;
}

.field small {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 5px;
}

.field input[type="file"] {
    padding: 12px;
    border: 2px dashed #d1d5db;
    background: #f9fafb;
    cursor: pointer;
}

.field input[type="file"]:hover {
    border-color: #00d4aa;
    background: rgba(0, 212, 170, 0.05);
}

.detailed-submit-btn {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    border: none;
    padding: 20px 50px;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.detailed-submit-btn:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(30, 58, 138, 0.4);
}

.detailed-submit-btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.detailed-submit-btn:hover i {
    transform: translateX(5px);
}

/* Office Location Map Section */
.map-area {
    background: white;
    padding: 80px 0;
}

.map-area .wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.map-header {
    text-align: center;
    margin-bottom: 50px;
}

.map-header h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 15px;
    line-height: 1.2;
}

.map-header p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.6;
}

.map-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 50px;
    align-items: start;
}

.map-info {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.office-details h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 30px;
}

.office-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 25px;
    padding: 15px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.office-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.office-item i {
    font-size: 1.2rem;
    color: #3b82f6;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 2px;
}

.office-item p {
    font-size: 1rem;
    color: #374151;
    line-height: 1.6;
    margin: 0;
    font-weight: 500;
}

.map-frame {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.map-frame iframe {
    width: 100%;
    height: 400px;
    border: none;
    display: block;
}

/* Mobile Responsive for Detailed Form and Map */
@media (max-width: 768px) {
    .detailed-contact-area {
        padding: 60px 0;
    }

    .section-header h2 {
        font-size: 2.2rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    .detailed-visa-form {
        padding: 30px 25px;
        margin: 0 10px;
    }

    .form-section h3 {
        font-size: 1.3rem;
    }

    .two-cols {
        grid-template-columns: 1fr;
    }

    .detailed-submit-btn {
        padding: 18px 30px;
        font-size: 1rem;
        width: 100%;
        justify-content: center;
    }

    .map-area {
        padding: 60px 0;
    }

    .map-header h2 {
        font-size: 2rem;
    }

    .map-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .map-info {
        padding: 30px 25px;
    }

    .office-details h3 {
        font-size: 1.4rem;
        margin-bottom: 25px;
    }

    .office-item {
        padding: 12px;
        margin-bottom: 20px;
    }

    .map-frame iframe {
        height: 300px;
    }
}

/* Contact Form Styles */
/* Reset & Base */
.wrapper {
  width: 90%;
  max-width: 900px;
  margin: auto;
  padding: 40px 0;
}

/* HERO */
.hero {
  position: relative;
  height: 350px;
  background: url('https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80px;
}
.hero-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 50, 0.6);
}
.hero-inner {
  position: relative;
  text-align: center;
  color: #fff;
  animation: slideIn 1s ease-out forwards;
}
.hero-inner h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}
.hero-inner p {
  font-size: 1.1rem;
}

/* CONTACT AREA */
.contact-area {
  background: #fff;
}
.contact-area h2 {
  text-align: center;
  margin-bottom: 30px;
  font-size: 2rem;
  color: #000080;
}

/* Form Grid */
.grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}
.two-cols .field {
  flex: 1 1 calc(50% - 20px);
}
.full {
  flex: 1 1 100%;
}
.field label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #000080;
}
.field input,
.field select,
.field textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.3s;
}
.field input:focus,
.field select:focus,
.field textarea:focus {
  border-color: #000080;
  outline: none;
}

/* Submit Button */
.btn-submit {
  display: block;
  margin: 30px auto 0;
  padding: 14px 0;
  width: 220px;
  background: #000080;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s;
}
.btn-submit:hover {
  background: #000060;
}

/* MAP AREA */
.map-area {
  background: #fff;
}
.map-area h2 {
  text-align: center;
  margin-bottom: 20px;
  font-size: 2rem;
  color: #000080;
}
.map-frame {
  border-radius: 8px;
  overflow: hidden;
}

/* Animations */
@keyframes slideIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive */
@media (max-width: 768px) {
  .two-cols .field {
    flex: 1 1 100%;
  }
  .hero-inner h1 {
    font-size: 2.2rem;
  }
  .hero {
    height: 250px;
  }
}

/* FEATURES CONTAINER */
.features-area {
  background: #f9fbff;
  padding: 80px 0;
}
.features-area h2 {
  text-align: center;
  color: #000080;
  font-size: 2.2rem;
  margin-bottom: 60px;
  position: relative;
}
.features-area h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 4px;
  background: #000080;
  margin: 16px auto 0;
  border-radius: 2px;
}

/* GRID LAYOUT */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 40px;
}

/* INDIVIDUAL CARD */
.feature {
  background: #fff;
  border-radius: 12px;
  padding: 50px 20px 30px;
  text-align: center;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.05);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  position: relative;
}
.feature:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* ICON HOLDER */
.icon-wrapper {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #000080 0%, #0033a0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}
.feature:hover .icon-wrapper {
  background: linear-gradient(135deg, #0033a0 0%, #000080 100%);
}
.icon-wrapper img {
  width: 40px;
  height: 40px;
  filter: invert(100%);
}

/* TITLE & TEXT */
.feature h3 {
  font-size: 1.25rem;
  color: #000080;
  margin-bottom: 12px;
}
.feature p {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* ACCENT STRIP */
.feature::before {
  content: '';
  position: absolute;
  top: 0;
  left: 25px;
  width: calc(100% - 50px);
  height: 4px;
  background: #000080;
  border-radius: 2px 2px 0 0;
}

/* RESPONSIVE TWEAKS */
@media (max-width: 768px) {
  .features-area {
    padding: 60px 0;
  }
  .features-area h2 {
    font-size: 1.9rem;
    margin-bottom: 40px;
  }
}

/* — Contact Card Section — */
.contact-card-section {
  background: linear-gradient(135deg, #f0f4fc, #ffffff);
  padding: 80px 0;
}

/* Centered wrapper with max-width */
.card-wrapper {
  width: 90%;
  max-width: 800px;
  margin: auto;
  text-align: center;
}
.card-wrapper h2 {
  color: #000080;
  font-size: 2.2rem;
  margin-bottom: 1rem;
  position: relative;
}
.card-wrapper h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 4px;
  background: #000080;
  border-radius: 2px;
  margin: 8px auto 0;
}

/* White card container */
.contact-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.06);
  padding: 40px;
  text-align: left;
}

/* Two-column form grid */
.contact-card form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
}

/* Full-width element override */
.contact-card form .full {
  grid-column: 1 / -1;
}

/* Field styling */
.contact-card .field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-card .field label {
  font-weight: 600;
  color: #000080;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

/* Input container with icon */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: #666;
  font-size: 1.1rem;
  z-index: 2;
  pointer-events: none;
}

/* Input styling */
.contact-card input,
.contact-card select,
.contact-card textarea {
  width: 100%;
  padding: 14px 12px 14px 40px;
  border: 1px solid #ccc;
  border-radius: 6px;
  transition: border-color 0.3s, box-shadow 0.3s;
  font-size: 0.95rem;
  background: #fff;
}

.contact-card textarea {
  resize: vertical;
  line-height: 1.6;
  font-family: inherit;
}

/* Focus state */
.contact-card input:focus,
.contact-card select:focus,
.contact-card textarea:focus {
  border-color: #000080;
  box-shadow: 0 0 0 3px rgba(0, 0, 128, 0.1);
  outline: none;
}

.contact-card input:focus + .input-icon,
.contact-card select:focus + .input-icon,
.contact-card textarea:focus + .input-icon {
  color: #000080;
}

/* File input styling */
.file-input {
  border: 2px dashed #ccc;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  transition: border-color 0.3s;
  cursor: pointer;
}

.file-input:hover {
  border-color: #000080;
}

.file-input input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  padding: 0;
  border: none;
}

.file-text {
  color: #666;
  font-size: 0.9rem;
  margin-left: 8px;
}

/* Error styling */
.field.error input,
.field.error select,
.field.error textarea {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: 4px;
  display: none;
}

.field.error .error-message {
  display: block;
}

/* Submit button */
.contact-card .btn-submit {
  grid-column: 1 / -1;
  max-width: 260px;
  margin: 24px auto 0;
  padding: 14px 20px;
  background: #000080;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s, transform 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.contact-card .btn-submit:hover {
  background: #000060;
  transform: translateY(-2px);
}

.contact-card .btn-submit:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Success card */
.success-card {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.success-icon i {
  font-size: 2rem;
  color: white;
}

.success-card h3 {
  color: #000080;
  font-size: 1.8rem;
  margin-bottom: 15px;
}

.success-card p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 25px;
}

.btn-new-request {
  background: #000080;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s;
}

.btn-new-request:hover {
  background: #000060;
}

/* Responsive tweak */
@media (max-width: 600px) {
  .contact-card {
    padding: 24px;
  }

  .contact-card form {
    grid-template-columns: 1fr;
  }

  .card-wrapper h2 {
    font-size: 1.8rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.success-icon {
  animation: pulse 2s infinite;
}

/* Detailed Visa Contact Form */
.detailed-contact-area {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
    position: relative;
}

.detailed-contact-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center center / cover no-repeat;
    opacity: 0.05;
    z-index: 1;
}

.detailed-contact-area .wrapper {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.8rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 20px;
    line-height: 1.2;
}

.section-header p {
    font-size: 1.2rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.detailed-visa-form {
    background: white;
    border-radius: 16px;
    padding: 50px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.detailed-visa-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #3b82f6, #00d4aa, #f59e0b);
    border-radius: 16px 16px 0 0;
}

.form-section {
    margin-bottom: 45px;
    padding-bottom: 35px;
    border-bottom: 1px solid #e5e7eb;
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 35px;
}

.form-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #00d4aa, #3b82f6);
    border-radius: 2px;
}

.grid {
    display: grid;
    gap: 25px;
    margin-bottom: 25px;
}

.two-cols {
    grid-template-columns: 1fr 1fr;
}

.field {
    display: flex;
    flex-direction: column;
    gap: 8px;
    position: relative;
}

.field.full {
    grid-column: 1 / -1;
}

.field label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 5px;
}

.field input,
.field select,
.field textarea {
    padding: 15px 18px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    color: #1f2937;
    background: white;
    transition: all 0.3s ease;
    font-family: inherit;
}

.field input:focus,
.field select:focus,
.field textarea:focus {
    outline: none;
    border-color: #00d4aa;
    box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.field input::placeholder,
.field textarea::placeholder {
    color: #9ca3af;
}

.field textarea {
    resize: vertical;
    line-height: 1.6;
}

.field small {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 5px;
}

.field input[type="file"] {
    padding: 12px;
    border: 2px dashed #d1d5db;
    background: #f9fafb;
    cursor: pointer;
}

.field input[type="file"]:hover {
    border-color: #00d4aa;
    background: rgba(0, 212, 170, 0.05);
}

.detailed-submit-btn {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    border: none;
    padding: 20px 50px;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.detailed-submit-btn:hover {
    background: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(30, 58, 138, 0.4);
}

.detailed-submit-btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.detailed-submit-btn:hover i {
    transform: translateX(5px);
}

/* Services Page Specific Styles */
.services-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.services-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.services-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Main Services Section */
.main-services-section {
    background: white;
    padding: 100px 0;
}

.main-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.main-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.main-service-card {
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.5s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.main-service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.main-service-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.main-service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    transition: all 0.4s ease;
}

.main-service-card:hover .main-service-icon {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    transform: scale(1.1);
}

.main-service-icon i {
    font-size: 2rem;
    color: #3b82f6;
    transition: all 0.4s ease;
}

.main-service-card:hover .main-service-icon i {
    color: white;
}

.main-service-card h3 {
    font-size: 1.6rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.main-service-card p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 25px;
    font-size: 1rem;
}

.service-features {
    list-style: none;
    margin-bottom: 30px;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #374151;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.service-features li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    width: 18px;
    height: 18px;
    background: #d1fae5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.main-service-btn {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.main-service-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.main-service-btn i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.main-service-btn:hover i {
    transform: translateX(3px);
}

/* Process Section */
.process-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 100px 0;
}

.process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.process-step {
    background: white;
    border-radius: 20px;
    padding: 35px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
}

.process-step:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
}

.process-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
}

.process-step h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.process-step p {
    color: #6b7280;
    line-height: 1.6;
}

/* Mobile Responsive for Services Page */
@media (max-width: 768px) {
    .services-hero-section {
        padding: 80px 0 60px 0;
    }

    .services-hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .main-services-section,
    .process-section {
        padding: 60px 0;
    }

    .main-services-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 40px;
    }

    .main-service-card {
        padding: 30px 25px;
    }

    .main-service-icon {
        width: 70px;
        height: 70px;
    }

    .main-service-icon i {
        font-size: 1.8rem;
    }

    .main-service-card h3 {
        font-size: 1.4rem;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-top: 40px;
    }

    .process-step {
        padding: 25px;
    }
}

/* Locomotive Scroll Styles */
html {
    scroll-behavior: auto;
}

html.has-scroll-smooth {
    overflow: hidden;
}

html.has-scroll-dragging {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.has-scroll-smooth body {
    overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
    min-height: 100vh;
}

/* Custom Scrollbar */
.c-scrollbar {
    position: absolute;
    right: 0;
    top: 0;
    width: 11px;
    height: 100%;
    transform-origin: center right;
    transition: transform 0.3s, opacity 0.3s;
    opacity: 0;
}

.c-scrollbar:hover {
    transform: scaleX(1.45);
}

.c-scrollbar:hover,
.has-scroll-scrolling .c-scrollbar,
.has-scroll-dragging .c-scrollbar {
    opacity: 1;
}

.c-scrollbar_thumb {
    position: absolute;
    top: 0;
    right: 0;
    background-color: rgba(59, 130, 246, 0.6);
    opacity: 0.8;
    width: 7px;
    border-radius: 10px;
    margin: 2px;
    cursor: -webkit-grab;
    cursor: grab;
}

.has-scroll-dragging .c-scrollbar_thumb {
    cursor: -webkit-grabbing;
    cursor: grabbing;
}

/* Smooth Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.is-revealed {
    opacity: 1;
    transform: translateY(0);
}

.slide-up {
    opacity: 0;
    transform: translateY(60px);
    transition: all 0.8s ease;
}

.slide-up.is-revealed {
    opacity: 1;
    transform: translateY(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.is-revealed {
    opacity: 1;
    transform: scale(1);
}

/* Hero Sections - Y-Axis Style */
.study-hero-section,
.migrate-hero-section,
.work-hero-section,
.visit-hero-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #3b82f6 100%);
    padding: 120px 0 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.study-hero-container,
.migrate-hero-container,
.work-hero-container,
.visit-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.study-hero-content,
.migrate-hero-content,
.work-hero-content,
.visit-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

/* Common Hero Elements */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: float 8s ease-in-out infinite;
    opacity: 0.6;
}

.floating-icon i {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 12px 20px;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    margin-bottom: 25px;
    width: fit-content;
    transition: all 0.3s ease;
}

.hero-badge:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.badge-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-icon i {
    font-size: 0.8rem;
    color: white;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 25px;
    color: white;
}

.title-main {
    display: block;
    color: white;
}

.title-highlight {
    display: block;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 35px;
    max-width: 600px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 10px 18px;
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.feature-icon {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon i {
    font-size: 0.7rem;
    color: white;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #60a5fa, #34d399);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 1rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.4rem;
    font-weight: 800;
    color: white;
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    margin-top: 2px;
}

.hero-cta-section {
    margin-top: 40px;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.primary-cta, .secondary-cta {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.primary-cta {
    background: linear-gradient(135deg, #60a5fa, #34d399);
    color: white;
    box-shadow: 0 8px 25px rgba(96, 165, 250, 0.3);
}

.primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(96, 165, 250, 0.4);
    background: linear-gradient(135deg, #3b82f6, #10b981);
}

.secondary-cta {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.secondary-cta:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.cta-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cta-note {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.cta-note i {
    font-size: 0.9rem;
    color: #34d399;
}

.hero-right {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.hero-visual {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.main-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.main-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.main-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(52, 211, 153, 0.2));
    display: flex;
    align-items: flex-end;
    padding: 25px;
}

.success-badge {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 50px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #1e3a8a;
    font-size: 0.9rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.success-badge i {
    color: #f59e0b;
    font-size: 1rem;
}

.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.info-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: cardFloat 6s ease-in-out infinite;
    max-width: 200px;
}

.card-1 {
    top: 20px;
    right: -50px;
    animation-delay: 0s;
}

.card-2 {
    bottom: 120px;
    left: -60px;
    animation-delay: 2s;
}

.card-3 {
    bottom: 20px;
    right: -40px;
    animation-delay: 4s;
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

.card-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon i {
    font-size: 0.9rem;
    color: white;
}

.card-content h4 {
    font-size: 0.85rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 2px;
    line-height: 1.2;
}

.card-content p {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.3;
}

/* Mobile Responsive for Hero Sections */
@media (max-width: 768px) {
    .study-hero-section,
    .migrate-hero-section,
    .work-hero-section,
    .visit-hero-section {
        padding: 80px 0 60px 0;
    }

    .study-hero-content,
    .migrate-hero-content,
    .work-hero-content,
    .visit-hero-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-features {
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .primary-cta, .secondary-cta {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-visual {
        margin-top: 30px;
    }

    .main-image img {
        height: 300px;
    }

    .floating-cards {
        display: none;
    }
}

/* Visit Page Specific Styles */
.visit-services-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
    padding: 80px 0;
    position: relative;
}

.visit-services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.visit-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e3a8a;
    margin-bottom: 15px;
    line-height: 1.2;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.service-icon i {
    font-size: 1.5rem;
    color: #3b82f6;
}

.service-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.service-card p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-card ul {
    list-style: none;
    margin-bottom: 25px;
}

.service-card ul li {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #374151;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.service-card ul li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    width: 16px;
    height: 16px;
    background: #d1fae5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.service-btn {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
    width: 100%;
    text-align: center;
}

.service-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

/* Quick Assessment Form */
.quick-assessment-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-top: 60px;
    position: relative;
    z-index: 3;
}

.assessment-container {
    max-width: 800px;
    margin: 0 auto;
}

.assessment-header {
    text-align: center;
    margin-bottom: 30px;
}

.assessment-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
}

.assessment-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #60a5fa;
    background: rgba(255, 255, 255, 0.15);
}

.assessment-submit {
    background: linear-gradient(135deg, #60a5fa, #34d399);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

/* Destinations Section */
.destinations-section {
    background: white;
    padding: 80px 0;
}

.destinations-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.destination-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.4s ease;
}

.destination-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.destination-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.destination-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.destination-card:hover .destination-image img {
    transform: scale(1.1);
}

.destination-overlay {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.destination-flag {
    font-size: 1.5rem;
}

.destination-content {
    padding: 25px;
}

.destination-content h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.visa-types {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.visa-tag {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.destination-stats {
    margin-bottom: 20px;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f3f4f6;
}

.stat:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.stat-value {
    font-size: 0.9rem;
    color: #1e3a8a;
    font-weight: 600;
}

.destination-btn {
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-block;
    width: 100%;
    text-align: center;
}

.destination-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

/* Mobile Responsive for Visit Page */
@media (max-width: 768px) {
    .visit-services-section {
        padding: 60px 0;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .service-card {
        padding: 25px;
    }

    .destinations-section {
        padding: 60px 0;
    }

    .destinations-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
    }

    .destination-card {
        margin: 0 10px;
    }

    .destination-image {
        height: 180px;
    }

    .destination-content {
        padding: 20px;
    }

    .destination-content h3 {
        font-size: 1.2rem;
    }

    .visa-types {
        margin-bottom: 15px;
    }

    .visa-tag {
        font-size: 0.75rem;
        padding: 5px 10px;
    }

    .quick-assessment-section {
        padding: 30px 20px;
        margin-top: 40px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .assessment-header h3 {
        font-size: 1.5rem;
    }
}

/* About Page Styles */
.about-hero {
    background:url(./images/hero/United\ Kingdom.jpeg);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

/* Modern About Page Styles */
/* Global Container */
.container {
  width: 90%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 60px 0;
}

/* SIMPLIFIED HERO SECTION */
.simplified-hero {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 50%, #81d4fa 100%);
  padding: 120px 0 80px;
  margin-top: 80px;
  min-height: auto;
}

.simplified-hero .hero-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.simplified-hero .hero-content {
  text-align: center;
  color: #1e293b;
}

.simplified-hero h1 {
  font-size: 2.8rem;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 30px;
  line-height: 1.2;
}

.hero-description {
  margin-bottom: 50px;
}

.hero-description p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #334155;
  margin-bottom: 20px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.why-choose-section {
  margin-bottom: 40px;
}

.why-choose-section h2 {
  font-size: 2.2rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 40px;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
  text-align: left;
}

.feature-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 25px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.9);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #0f172a;
  margin-bottom: 8px;
}

.feature-content p {
  color: #475569;
  line-height: 1.6;
  font-size: 0.95rem;
}

.hero-conclusion {
  margin-bottom: 40px;
}

.hero-conclusion p {
  font-size: 1.1rem;
  font-weight: 500;
  color: #1e293b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.simplified-hero .btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 15px 30px;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

.simplified-hero .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.simplified-hero .btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #1e293b;
  padding: 15px 30px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.simplified-hero .btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-2px);
}

/* Responsive Design for Simplified Hero */
@media (max-width: 768px) {
  .simplified-hero h1 {
    font-size: 2.2rem;
  }

  .why-choose-section h2 {
    font-size: 1.8rem;
  }

  .features-list {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-item {
    padding: 20px;
  }

  .hero-actions {
    flex-direction: column;
    gap: 15px;
  }

  .simplified-hero .btn-primary,
  .simplified-hero .btn-secondary {
    width: 100%;
    justify-content: center;
    max-width: 300px;
  }
}

/* Animated Background */
.hero-background-wrapper {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.animated-background {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
              linear-gradient(135deg, #0a0f1c 0%, #1a1f2e 50%, #2a2f3e 100%);
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: orbFloat 8s ease-in-out infinite;
}

.orb-1 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4), rgba(168, 85, 247, 0.4));
  top: -200px;
  right: -200px;
  animation-delay: 0s;
}

.orb-2 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, rgba(236, 72, 153, 0.3), rgba(239, 68, 68, 0.3));
  bottom: -150px;
  left: -150px;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.3), rgba(59, 130, 246, 0.3));
  top: 50%;
  left: 30%;
  animation-delay: 4s;
}

.orb-4 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(34, 197, 94, 0.3));
  top: 20%;
  right: 30%;
  animation-delay: 6s;
}

.hero-overlay-modern {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(10, 15, 28, 0.8), rgba(26, 31, 46, 0.6));
  backdrop-filter: blur(1px);
}

/* Hero Content */
.hero-content-wrapper {
  position: relative;
  z-index: 10;
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.hero-main-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  width: 100%;
}

.hero-text-section {
  color: white;
  animation: slideInLeft 1s ease-out;
}

/* Hero Badge */
.hero-intro-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 50px;
  margin-bottom: 30px;
  overflow: hidden;
}

.badge-icon {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
}

.badge-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.5px;
}

.badge-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  border-radius: 50px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hero-intro-badge:hover .badge-glow {
  opacity: 1;
}

/* Hero Title */
.hero-main-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.title-line-1 {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.title-line-2 {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.highlight-text {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.animated-text {
  color: white;
  position: relative;
  overflow: hidden;
}

.animated-text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  animation: underlineGrow 2s ease-in-out infinite;
}

/* Hero Subtitle */
.hero-subtitle {
  font-size: 1.2rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
}

/* Hero Metrics */
.hero-metrics {
  display: flex;
  gap: 20px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.metric-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 25px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 180px;
}

.metric-card:hover {
  transform: translateY(-5px);
  border-color: rgba(99, 102, 241, 0.5);
}

.metric-card.featured-metric {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
}

.metric-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.3rem;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
}

.metric-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-bottom: 2px;
}

.metric-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.metric-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover .metric-glow {
  opacity: 1;
}

.featured-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 0.6rem;
  font-weight: 600;
}

/* Hero CTA Section */
.hero-cta-section {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.cta-primary {
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  padding: 18px 30px;
  border-radius: 15px;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(99, 102, 241, 0.4);
}

.cta-secondary {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 16px 28px;
  border-radius: 15px;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.cta-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.cta-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cta-main {
  font-size: 1rem;
  font-weight: 600;
}

.cta-sub {
  font-size: 0.8rem;
  opacity: 0.8;
}

.cta-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border-radius: 15px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cta-primary:hover .cta-glow {
  opacity: 1;
}

/* Hero Visual Section */
.hero-visual-section {
  position: relative;
  animation: slideInRight 1s ease-out;
}

.visual-container {
  position: relative;
  width: 100%;
  height: 600px;
}

.main-visual-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.main-visual-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 35px 70px rgba(0, 0, 0, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.card-menu {
  display: flex;
  gap: 4px;
}

.menu-dot {
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
}

.card-content {
  position: relative;
  height: 400px;
}

.consultation-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.consultation-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 25px 25px;
}

.consultant-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.consultant-avatar {
  position: relative;
  width: 50px;
  height: 50px;
}

.consultant-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 14px;
  height: 14px;
  background: #10b981;
  border: 2px solid white;
  border-radius: 50%;
}

.consultant-details {
  color: white;
}

.consultant-name {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 2px;
}

.consultant-role {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Floating Notifications */
.floating-notifications {
  position: absolute;
  top: 50px;
  right: -50px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 5;
}

.notification {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: slideInRight 0.6s ease-out;
  min-width: 280px;
}

.notification:hover {
  transform: translateX(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.success-notification {
  border-left: 4px solid #10b981;
  animation-delay: 0.2s;
}

.info-notification {
  border-left: 4px solid #3b82f6;
  animation-delay: 0.4s;
}

.support-notification {
  border-left: 4px solid #8b5cf6;
  animation-delay: 0.6s;
}

.notification-icon {
  width: 35px;
  height: 35px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.success-notification .notification-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.info-notification .notification-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.support-notification .notification-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.notification-subtitle {
  font-size: 0.8rem;
  color: #6b7280;
}

.notification-time {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: 500;
}

/* Achievement Badges */
.achievement-badges {
  position: absolute;
  bottom: 50px;
  left: -30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 5;
}

.achievement-badge {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: slideInLeft 0.6s ease-out;
}

.achievement-badge:hover {
  transform: translateX(5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.achievement-badge:first-child {
  animation-delay: 0.8s;
}

.achievement-badge:last-child {
  animation-delay: 1s;
}

.achievement-badge i {
  color: #f59e0b;
  font-size: 1rem;
}

.achievement-badge span {
  font-size: 0.8rem;
  font-weight: 600;
  color: #1f2937;
}

/* Scroll Indicator */
.hero-scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.7);
  animation: bounce 2s infinite;
  z-index: 10;
}

.scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

/* Animations */
@keyframes orbFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes underlineGrow {
  0%, 100% {
    width: 0%;
  }
  50% {
    width: 100%;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

/* Responsive Design for Ultra Modern Hero */
@media (max-width: 1024px) {
  .hero-main-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  .hero-main-title {
    font-size: 3rem;
  }

  .floating-notifications {
    position: static;
    margin-top: 30px;
  }

  .achievement-badges {
    position: static;
    flex-direction: row;
    justify-content: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .hero-main-title {
    font-size: 2.5rem;
  }

  .title-line-2 {
    flex-direction: column;
    gap: 5px;
  }

  .hero-metrics {
    justify-content: center;
    gap: 15px;
  }

  .metric-card {
    min-width: 150px;
    padding: 20px 15px;
  }

  .hero-cta-section {
    justify-content: center;
    flex-direction: column;
    gap: 15px;
  }

  .visual-container {
    height: 400px;
  }

  .floating-notifications {
    gap: 10px;
  }

  .notification {
    min-width: 250px;
    padding: 12px 15px;
  }
}

/* Hero Visual */
.hero-visual {
  position: relative;
}

.hero-image-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.hero-image {
  width: 100%;
  height: 500px;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  inset: 0;
}

.success-badge, .support-badge {
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  animation: float 3s ease-in-out infinite;
}

.success-badge {
  top: 20px;
  right: 20px;
  color: #059669;
  animation-delay: 0s;
}

.support-badge {
  bottom: 20px;
  left: 20px;
  color: #3b82f6;
  animation-delay: 1.5s;
}

/* MODERN MISSION & VISION */
.modern-mv-section {
  padding: 100px 0;
  background: #f8fafc;
}

.mv-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  color: #1e40af;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 20px;
  border: 1px solid #bfdbfe;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  margin-bottom: 20px;
}

.section-header p {
  font-size: 1.1rem;
  color: #64748b;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
}

.mv-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.mv-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mv-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.mv-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.card-badge {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  color: #1e40af;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
}

.mv-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 15px;
}

.mv-card p {
  color: #64748b;
  line-height: 1.7;
  margin-bottom: 25px;
}

.card-features {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #059669;
  font-size: 0.9rem;
}

.feature-item i {
  font-size: 1rem;
}

/* MODERN VALUES SECTION */
.modern-values-section {
  padding: 100px 0;
  background: white;
}

.values-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.value-card {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #f1f5f9;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.value-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin: 0 auto 25px;
  transition: all 0.3s ease;
}

.value-card:hover .value-icon {
  transform: scale(1.1);
}

.value-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 15px;
}

.value-card p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 20px;
}

.value-highlight {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  color: #1e40af;
  padding: 8px 16px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
}

/* MODERN TIMELINE */
.modern-timeline-section {
  padding: 100px 0;
  background: #f8fafc;
}

.timeline-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.modern-timeline {
  position: relative;
  margin-top: 60px;
}

.modern-timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

.timeline-item {
  position: relative;
  margin-bottom: 60px;
  padding-left: 100px;
}

.timeline-marker {
  position: absolute;
  left: 0;
  top: 0;
}

.marker-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.timeline-content {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.timeline-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.timeline-year {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 15px;
}

.timeline-content h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 10px;
}

.timeline-content p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 15px;
}

.timeline-stats {
  background: #f1f5f9;
  color: #475569;
  padding: 8px 16px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
}

/* MODERN TEAM SECTION */
.modern-team-section {
  padding: 100px 0;
  background: white;
}

.team-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.team-member {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.member-image {
  position: relative;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.member-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(29, 78, 216, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.team-member:hover .member-overlay {
  opacity: 1;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: white;
  color: #3b82f6;
  transform: scale(1.1);
}

.member-info {
  padding: 30px;
}

.member-info h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 5px;
}

.member-role {
  color: #3b82f6;
  font-weight: 600;
  margin-bottom: 15px;
}

.member-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 20px;
}

.member-expertise {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.expertise-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* MODERN STATS SECTION */
.modern-stats-section {
  position: relative;
  padding: 100px 0;
  overflow: hidden;
}

.stats-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.stats-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.8));
}

.stats-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.stats-content .section-header {
  color: white;
}

.stats-content .section-badge {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #60a5fa;
}

.stats-content .section-header h2 {
  color: white;
}

.stats-content .section-header p {
  color: rgba(255, 255, 255, 0.8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
}

.stat-card.featured {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
}

.stat-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin: 0 auto 25px;
}

.stat-number {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 5px;
  margin-bottom: 15px;
}

.stat-number .count {
  font-size: 3rem;
  font-weight: 700;
  color: white;
}

.stat-suffix {
  font-size: 2rem;
  font-weight: 600;
  color: #60a5fa;
}

.stat-card h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
}

.stat-card p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.featured-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
}

/* MODERN CTA SECTION */
.modern-cta-section {
  position: relative;
  padding: 100px 0;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
}

.cta-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(51, 65, 85, 0.8));
}

.cta-shapes {
  position: absolute;
  inset: 0;
}

.cta-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  animation: float 8s ease-in-out infinite;
}

.cta-shape-1 {
  width: 400px;
  height: 400px;
  top: -200px;
  right: -200px;
  animation-delay: 0s;
}

.cta-shape-2 {
  width: 300px;
  height: 300px;
  bottom: -150px;
  left: -150px;
  animation-delay: 4s;
}

.cta-container {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.cta-content {
  color: white;
}

.cta-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #60a5fa;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-features {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.cta-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #60a5fa;
  font-size: 0.9rem;
}

.cta-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.cta-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.cta-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.cta-btn-secondary {
  background: transparent;
  color: white;
  padding: 15px 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.cta-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.cta-trust {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

/* RESPONSIVE STYLES */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    justify-content: center;
    gap: 20px;
  }

  .hero-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .mv-grid {
    grid-template-columns: 1fr;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .modern-timeline::before {
    left: 15px;
  }

  .timeline-item {
    padding-left: 60px;
  }

  .marker-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .cta-features {
    flex-direction: column;
    gap: 15px;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .cta-trust {
    flex-direction: column;
    gap: 15px;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .cta-content h2 {
    font-size: 2rem;
  }
}

/* ANIMATIONS */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* HERO */
.hero-about {
  position: relative;
  height: 80vh;
  background: url('https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80px;
}
.hero-about .overlay {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.5);
}
.hero-about .hero-content {
  position: relative;
  text-align: center;
  color: #fff;
}
.hero-about h1 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}
.hero-about p {
  font-size: 1.2rem;
}

/* MISSION & VISION */
.mv-section {
  padding: 60px 0;
  background: #f8fafc;
}
.mv-section .container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  padding: 0 20px;
}
.mv-card {
  flex: 1 1 300px;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.05);
}
.mv-card h2 {
  color: #000080;
  margin-bottom: 15px;
}
.mv-card p {
  color: #555;
  line-height: 1.6;
}

/* CORE VALUES */
.values-section {
  padding: 60px 0;
  background: #fff;
}
.values-section h2 {
  text-align: center;
  color: #000080;
  margin-bottom: 40px;
  font-size: 2.2rem;
}
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit,minmax(180px,1fr));
  gap: 30px;
  text-align: center;
}
.value img {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  border-radius: 50%;
  object-fit: cover;
}
.value h4 {
  color: #000080;
  font-size: 1rem;
}

/* TIMELINE */
.timeline-section {
  padding: 60px 0;
  background: #f8fafc;
}
.timeline-section h2 {
  text-align: center;
  color: #000080;
  margin-bottom: 40px;
  font-size: 2.2rem;
}
.timeline {
  list-style: none;
  padding: 0;
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}
.timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  width: 4px;
  height: 100%;
  background: #000080;
}
.timeline li {
  position: relative;
  margin-bottom: 40px;
  padding-left: 60px;
}
.timeline .year {
  position: absolute;
  left: -4px;
  top: 0;
  background: #000080;
  color: #fff;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}
.timeline .desc {
  color: #333;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* TEAM */
.team-section {
  padding: 60px 0;
  background: #fff;
}
.team-section h2 {
  text-align: center;
  color: #000080;
  margin-bottom: 40px;
  font-size: 2.2rem;
}
.team-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  justify-content: center;
}
.member {
  flex: 1 1 200px;
  text-align: center;
}
.member img {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 8px 20px rgba(0,0,0,0.05);
  margin-bottom: 15px;
}
.member h4 {
  color: #000080;
  margin-bottom: 5px;
}
.member p {
  color: #555;
  font-size: 0.9rem;
}

/* STATS */
.stats-section {
  background: #f9fbff;
  padding: 60px 0;
}
.stats-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  justify-content: center;
}
.stat {
  text-align: center;
  flex: 1 1 180px;
}
.stat .count {
  font-size: 2.5rem;
  color: #000080;
  display: block;
  font-weight: 700;
}
.stat p {
  color: #555;
  font-size: 1rem;
  margin-top: 10px;
}

/* CTA */
.cta-section {
  text-align: center;
  padding: 60px 0;
  background: #fff;
}
.cta-section h2 {
  color: #000080;
  margin-bottom: 20px;
  font-size: 2rem;
}
.cta-section p {
  color: #555;
  margin-bottom: 30px;
}
.btn-primary {
  background: #000080;
  color: #fff;
  padding: 14px 30px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 1rem;
  transition: background 0.3s;
  display: inline-block;
}
.btn-primary:hover {
  background: #000060;
}

/* RESPONSIVE */
@media (max-width: 768px) {
  .mv-section .container {
    flex-direction: column;
  }
  .timeline::before {
    left: 50%;
  }
  .timeline li {
    padding-left: 0;
    text-align: center;
  }
  .timeline .year {
    left: 50%;
    transform: translateX(-50%);
  }
  .hero-about h1 {
    font-size: 2.2rem;
  }
  .hero-about {
    height: 60vh;
  }
  .stats-grid {
    gap: 30px;
  }
  .team-grid {
    gap: 25px;
  }
}

/* Company Excellence Section */
.company-excellence-section {
    padding: 100px 0;
    background: #f8fafc;
}

.excellence-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.excellence-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.excellence-left .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
    color: #1e40af;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    border: 1px solid #bfdbfe;
}

.excellence-left h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.2;
    margin-bottom: 25px;
}

.excellence-intro {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.7;
    margin-bottom: 40px;
}

.excellence-pillars {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.pillar-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.pillar-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.pillar-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.pillar-content p {
    color: #64748b;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Mobile Responsive for Company Excellence */
@media (max-width: 768px) {
    .company-excellence-section {
        padding: 60px 0;
    }

    .excellence-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .excellence-left h2 {
        font-size: 2rem;
    }

    .pillar-item {
        text-align: left;
    }
}

.about-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.about-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.company-profile {
    padding: 100px 0;
    background: white;
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.profile-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.profile-text h2 {
    font-size: 2.5rem;
    color: navy;
    margin-bottom: 40px;
}

.mission-vision {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.mission, .vision {
    padding: 30px;
    background: #f8fafc;
    border-radius: 15px;
    border-left: 4px solid navy;
}

.mission h3, .vision h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.mission h3 i, .vision h3 i {
    font-size: 1.5rem;
}

.why-choose-us {
    padding: 100px 0;
    background: #f8fafc;
}

.choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.choose-container h2 {
    font-size: 2.5rem;
    color: navy;
    margin-bottom: 60px;
}

.choose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.choose-item {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.choose-item:hover {
    transform: translateY(-5px);
}

.choose-item i {
    font-size: 3rem;
    color: navy;
    margin-bottom: 20px;
}

.choose-item h3 {
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.our-team {
    padding: 100px 0;
    background: white;
}

.team-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.team-container h2 {
    font-size: 2.5rem;
    color: navy;
    margin-bottom: 60px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
}

.team-member {
    background: #f8fafc;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
}

.member-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 20px;
    overflow: hidden;
    border: 4px solid navy;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member h3 {
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.team-member p {
    color: #666;
    margin-bottom: 5px;
}

.team-member span {
    color: #999;
    font-size: 0.9rem;
}

.our-office {
    padding: 100px 0;
    background: #f8fafc;
}

.office-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.office-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.office-info h2 {
    font-size: 2.5rem;
    color: navy;
    margin-bottom: 40px;
}

.office-details {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.detail-item i {
    font-size: 1.5rem;
    color: navy;
    margin-top: 5px;
}

.detail-item h4 {
    color: navy;
    font-size: 1.2rem;
    margin-bottom: 8px;
}

.about-cta {
    padding: 80px 0;
    background: navy;
    text-align: center;
    color: white;
}

.about-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.about-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.cta-button {
    background: white;
    color: navy;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: #f8fafc;
    transform: translateY(-2px);
}

/* Services Page Styles */
.services-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.services-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.services-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.visa-types {
    padding: 100px 0;
    background: white;
}

.types-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.types-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.visa-type-card {
    background: #f8fafc;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
    border: 1px solid #e5e7eb;
}

.visa-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.visa-type-card i {
    font-size: 3rem;
    color: navy;
    margin-bottom: 20px;
}

.visa-type-card h3 {
    color: navy;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.visa-type-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.visa-type-card ul {
    list-style: none;
    padding: 0;
    margin-bottom: 25px;
}

.visa-type-card ul li {
    color: #666;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.visa-type-card ul li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.learn-more {
    background: navy;
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.learn-more:hover {
    background: #1e40af;
}

.country-services {
    padding: 100px 0;
    background: #f8fafc;
}

.country-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.country-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.country-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.country-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.country-card:hover {
    transform: translateY(-5px);
}

.country-card img {
    width: 60px;
    height: 40px;
    object-fit: cover;
    margin-bottom: 20px;
    border-radius: 5px;
}

.country-card h3 {
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.country-link {
    background: navy;
    color: white;
    padding: 8px 16px;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.country-link:hover {
    background: #1e40af;
}

.value-services {
    padding: 100px 0;
    background: white;
}

.value-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.value-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.value-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.value-item {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.value-item i {
    font-size: 2.5rem;
    color: navy;
    margin-bottom: 20px;
}

.value-item h3 {
    color: navy;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.visa-process {
    padding: 100px 0;
    background: #f8fafc;
}

.process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.process-container h2 {
    font-size: 2.5rem;
    color: navy;
    margin-bottom: 60px;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.step {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: navy;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px;
}

.step h3 {
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.services-cta {
    padding: 80px 0;
    background: navy;
    text-align: center;
    color: white;
}

.services-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.services-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Contact Page Styles */
.contact-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.contact-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.contact-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.contact-main {
    padding: 100px 0;
    background: #f8fafc;
}

.faq-section {
    padding: 80px 0;
    background: white;
}

.faq-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.faq-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.faq-item {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    border-left: 4px solid navy;
}

.faq-item h3 {
    color: navy;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
}

/* Migration/Immigration Page Styles */
.migration-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.migration-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.migration-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.immigration-countries {
    padding: 100px 0;
    background: white;
}

.immigration-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.immigration-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.countries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.country-immigration-card {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease;
}

.country-immigration-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.country-immigration-card img {
    width: 60px;
    height: 40px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.country-immigration-card h3 {
    color: navy;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.programs-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 15px;
}

.program-tag {
    background: navy;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.country-immigration-card ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    text-align: left;
}

.country-immigration-card ul li {
    padding: 5px 0;
    position: relative;
    padding-left: 20px;
}

.country-immigration-card ul li::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.immigration-btn {
    background: navy;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.immigration-btn:hover {
    background: #1e40af;
    transform: translateY(-2px);
}

.immigration-process {
    padding: 100px 0;
    background: #f8fafc;
}

.process-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-step {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.step-icon {
    width: 80px;
    height: 80px;
    background: navy;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.step-icon i {
    font-size: 2rem;
    color: white;
}

.timeline-step h3 {
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.immigration-benefits {
    padding: 100px 0;
    background: white;
}

.benefits-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.benefits-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.benefit-item {
    text-align: center;
    padding: 30px 20px;
    background: #f8fafc;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.benefit-item i {
    font-size: 3rem;
    color: navy;
    margin-bottom: 20px;
}

.benefit-item h3 {
    color: navy;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.immigration-cta {
    padding: 80px 0;
    background: navy;
    text-align: center;
    color: white;
}

.immigration-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.immigration-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Study Page Styles */
.study-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.study-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.study-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.study-destinations {
    padding: 100px 0;
    background: white;
}

.study-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.study-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.study-destination-card {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease;
}

.study-destination-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.study-destination-card img {
    width: 60px;
    height: 40px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.study-destination-card h3 {
    color: navy;
    font-size: 1.5rem;
    margin-bottom: 15px;
    text-align: center;
}

.study-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 15px;
}

.highlight-tag {
    background: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.study-features {
    margin: 20px 0;
}

.study-features .feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.study-features .feature-item i {
    color: navy;
    font-size: 1rem;
}

.study-btn {
    background: navy;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: block;
    text-align: center;
    margin-top: 20px;
}

.study-btn:hover {
    background: #1e40af;
    transform: translateY(-2px);
}

.study-cta {
    padding: 80px 0;
    background: navy;
    text-align: center;
    color: white;
}

.study-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.study-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Work Page Styles */
.work-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.work-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.work-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.work-destinations {
    padding: 100px 0;
    background: white;
}

.work-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.work-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.work-destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.work-destination-card {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease;
}

.work-destination-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.work-destination-card img {
    width: 60px;
    height: 40px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.work-destination-card h3 {
    color: navy;
    font-size: 1.5rem;
    margin-bottom: 15px;
    text-align: center;
}

.work-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    margin-bottom: 15px;
}

.work-programs {
    margin: 20px 0;
}

.work-programs .program-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.work-programs .program-item i {
    color: navy;
    font-size: 1rem;
}

.work-btn {
    background: navy;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: block;
    text-align: center;
    margin-top: 20px;
}

.work-btn:hover {
    background: #1e40af;
    transform: translateY(-2px);
}

.in-demand-jobs {
    padding: 100px 0;
    background: #f8fafc;
}

.jobs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.jobs-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.jobs-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.job-category {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.job-category h3 {
    color: navy;
    font-size: 1.3rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.job-category h3 i {
    font-size: 1.5rem;
}

.job-category ul {
    list-style: none;
    padding: 0;
}

.job-category ul li {
    padding: 8px 0;
    border-bottom: 1px solid #e5e7eb;
    color: #666;
}

.job-category ul li:last-child {
    border-bottom: none;
}

.work-cta {
    padding: 80px 0;
    background: navy;
    text-align: center;
    color: white;
}

.work-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.work-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Coaching Page Styles */
.coaching-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.coaching-hero-container h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.coaching-hero-container p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.test-courses {
    padding: 100px 0;
    background: white;
}

.courses-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.courses-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.course-card {
    background: #f8fafc;
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    transition: transform 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.course-header {
    background: navy;
    color: white;
    padding: 20px 30px;
    position: relative;
}

.course-header h3 {
    font-size: 1.5rem;
    margin: 0;
}

.course-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: #f59e0b;
    color: white;
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.course-content {
    padding: 30px;
}

.course-content p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.course-features {
    margin-bottom: 25px;
}

.course-features .feature {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.course-features .feature i {
    color: #10b981;
    font-size: 1rem;
}

.course-details {
    margin-bottom: 25px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.course-details .detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #666;
}

.course-details .detail-item i {
    color: navy;
    font-size: 1rem;
}

.course-btn {
    background: navy;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: block;
    text-align: center;
    width: 100%;
}

.course-btn:hover {
    background: #1e40af;
    transform: translateY(-2px);
}

.coaching-features {
    padding: 100px 0;
    background: #f8fafc;
}

.features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.features-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.features-grid .feature-item {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.features-grid .feature-item:hover {
    transform: translateY(-5px);
}

.features-grid .feature-item i {
    font-size: 3rem;
    color: navy;
    margin-bottom: 20px;
}

.features-grid .feature-item h3 {
    color: navy;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.success-stories {
    padding: 100px 0;
    background: white;
}

.stories-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.stories-container h2 {
    font-size: 2.5rem;
    color: navy;
    text-align: center;
    margin-bottom: 60px;
}

.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.story-card {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    position: relative;
    border: 1px solid #e5e7eb;
}

.score-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #10b981;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
}

.story-card h3 {
    color: navy;
    font-size: 1.3rem;
    margin: 20px 0 15px;
}

.story-card p {
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
    line-height: 1.6;
}

.story-card .destination {
    color: #10b981;
    font-weight: 600;
    font-size: 0.9rem;
}

.coaching-cta {
    padding: 80px 0;
    background: navy;
    text-align: center;
    color: white;
}

.coaching-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.coaching-cta p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Consistent Navbar Height for All Pages */
nav {
    min-height: 80px;
    height: 80px;
    display: flex;
    align-items: center;
}
.desktop-menu ul {
    min-height: 80px;
    height: 80px;
    display: flex;
    align-items: center;
}
.logo {
    height: 60px;
}